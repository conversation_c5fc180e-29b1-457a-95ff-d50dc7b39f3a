import {
  ApplicationConfig,
  importProvidersFrom,
  provideAppInitializer,
  provideZoneChangeDetection,
} from '@angular/core';
import {
  provideRouter,
  withComponentInputBinding,
  withViewTransitions,
} from '@angular/router';

import {
  HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { httpTranslateLoaderFactory } from '@core/factory';
import { initializeApp } from '@core/factory/initialize';
import { initIcon } from '@core/init/heroicon';
import { authInterceptor } from '@core/interceptors';
import { provideIcons } from '@ng-icons/core';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { provideMonacoEditor } from 'ngx-monaco-editor-v2';
import { JoyrideModule } from 'ngx-joyride';
import { QuillModule } from 'ngx-quill';
import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideAppInitializer(initializeApp),
    provideAnimations(),
    provideAnimationsAsync(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withComponentInputBinding(), withViewTransitions()),
    provideHttpClient(withInterceptors([authInterceptor]), withFetch()),
    importProvidersFrom([
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: httpTranslateLoaderFactory,
          deps: [HttpClient],
        },
      }),
      JoyrideModule.forRoot(),
      QuillModule.forRoot(),
    ]),
    provideMonacoEditor(),
    provideIcons(initIcon),
  ],
};
