/* Quill wrapper styles */
.quill-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
}

/* Quill editor container styles */
::ng-deep quill-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

::ng-deep .ql-toolbar.ql-snow {
  @apply border border-primary-border dark:border-dark-primary-border;
  border-bottom: none;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  @apply bg-base-200 dark:bg-dark-base-200;
  padding: 8px;
}

::ng-deep .ql-container.ql-snow {
  @apply border border-primary-border dark:border-dark-primary-border;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  @apply bg-base-100 dark:bg-dark-base-100;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

::ng-deep .ql-editor {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.6;
  @apply text-base-content dark:text-dark-base-content;
}

::ng-deep .ql-editor.ql-blank::before {
  @apply text-neutral-content dark:text-dark-neutral-content;
  opacity: 0.6;
  font-style: normal;
  left: 16px;
  right: 16px;
}

::ng-deep .ql-editor p,
::ng-deep .ql-editor ol,
::ng-deep .ql-editor ul,
::ng-deep .ql-editor pre,
::ng-deep .ql-editor blockquote,
::ng-deep .ql-editor h1,
::ng-deep .ql-editor h2,
::ng-deep .ql-editor h3,
::ng-deep .ql-editor h4,
::ng-deep .ql-editor h5,
::ng-deep .ql-editor h6 {
  margin-bottom: 0.5em;
}

/* Toolbar button styles */
::ng-deep .ql-toolbar button {
  width: 28px !important;
  height: 28px !important;
  padding: 3px !important;
}

::ng-deep .ql-toolbar button:hover {
  @apply bg-base-300 dark:bg-dark-base-300;
  border-radius: 0.25rem;
}

::ng-deep .ql-toolbar button.ql-active {
  @apply bg-primary dark:bg-dark-primary text-white;
  border-radius: 0.25rem;
}

::ng-deep .ql-toolbar .ql-stroke {
  fill: none;
  @apply stroke-neutral-content dark:stroke-dark-neutral-content;
}

::ng-deep .ql-toolbar .ql-fill,
::ng-deep .ql-toolbar .ql-stroke.ql-fill {
  @apply fill-neutral-content dark:fill-dark-neutral-content;
  stroke: none;
}

::ng-deep .ql-toolbar .ql-picker {
  @apply text-base-content dark:text-dark-base-content;
}

::ng-deep .ql-toolbar .ql-picker-options {
  @apply bg-base-100 dark:bg-dark-base-100;
  @apply border border-primary-border dark:border-dark-primary-border;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

::ng-deep .ql-toolbar .ql-picker-label {
  @apply text-base-content dark:text-dark-base-content;
}

/* Tooltip styles */
::ng-deep .ql-snow .ql-tooltip {
  @apply bg-base-100 dark:bg-dark-base-100;
  @apply border border-primary-border dark:border-dark-primary-border;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  @apply text-base-content dark:text-dark-base-content;
  z-index: 1000;
}

::ng-deep .ql-snow .ql-tooltip input[type=text] {
  @apply bg-base-200 dark:bg-dark-base-200;
  @apply border border-primary-border dark:border-dark-primary-border;
  @apply text-base-content dark:text-dark-base-content;
}

/* Active/hover states */
::ng-deep .ql-toolbar button:hover .ql-fill,
::ng-deep .ql-toolbar button:hover .ql-stroke,
::ng-deep .ql-toolbar button.ql-active .ql-fill,
::ng-deep .ql-toolbar button.ql-active .ql-stroke {
  @apply fill-primary dark:fill-dark-primary;
  @apply stroke-primary dark:stroke-dark-primary;
}

::ng-deep .ql-toolbar .ql-picker-label:hover,
::ng-deep .ql-toolbar .ql-picker-label.ql-active,
::ng-deep .ql-toolbar .ql-picker-item:hover,
::ng-deep .ql-toolbar .ql-picker-item.ql-selected {
  @apply text-primary dark:text-dark-primary;
}

/* Read-only state */
::ng-deep quill-editor[ng-reflect-read-only="true"] .ql-toolbar {
  display: none;
}

::ng-deep quill-editor[ng-reflect-read-only="true"] .ql-container {
  border-radius: 0.5rem;
}

::ng-deep quill-editor[ng-reflect-read-only="true"] .ql-editor {
  cursor: default;
}

/* Format specific styles */
::ng-deep .ql-editor h1 {
  font-size: 2em;
  font-weight: bold;
}

::ng-deep .ql-editor h2 {
  font-size: 1.5em;
  font-weight: bold;
}

::ng-deep .ql-editor h3 {
  font-size: 1.17em;
  font-weight: bold;
}

::ng-deep .ql-editor blockquote {
  @apply border-l-4 border-primary dark:border-dark-primary;
  padding-left: 16px;
  margin-left: 0;
  opacity: 0.8;
}

::ng-deep .ql-editor pre {
  @apply bg-base-200 dark:bg-dark-base-200;
  border-radius: 0.25rem;
  padding: 12px;
  overflow-x: auto;
}

::ng-deep .ql-editor code {
  @apply bg-base-200 dark:bg-dark-base-200;
  padding: 2px 4px;
  border-radius: 0.25rem;
  font-family: monospace;
}

/* Fix for Quill's default styles */
::ng-deep .ql-snow.ql-toolbar button:hover,
::ng-deep .ql-snow .ql-toolbar button:hover,
::ng-deep .ql-snow.ql-toolbar button:focus,
::ng-deep .ql-snow .ql-toolbar button:focus,
::ng-deep .ql-snow.ql-toolbar button.ql-active,
::ng-deep .ql-snow .ql-toolbar button.ql-active,
::ng-deep .ql-snow.ql-toolbar .ql-picker-label:hover,
::ng-deep .ql-snow .ql-toolbar .ql-picker-label:hover,
::ng-deep .ql-snow.ql-toolbar .ql-picker-label.ql-active,
::ng-deep .ql-snow .ql-toolbar .ql-picker-label.ql-active,
::ng-deep .ql-snow.ql-toolbar .ql-picker-item:hover,
::ng-deep .ql-snow .ql-toolbar .ql-picker-item:hover,
::ng-deep .ql-snow.ql-toolbar .ql-picker-item.ql-selected,
::ng-deep .ql-snow .ql-toolbar .ql-picker-item.ql-selected {
  @apply text-primary dark:text-dark-primary;
}

/* Ensure proper icon colors */
::ng-deep .ql-snow .ql-stroke {
  @apply stroke-neutral-content dark:stroke-dark-neutral-content;
}

::ng-deep .ql-snow .ql-fill,
::ng-deep .ql-snow .ql-stroke.ql-fill {
  @apply fill-neutral-content dark:fill-dark-neutral-content;
}

::ng-deep .ql-snow .ql-picker {
  @apply text-base-content dark:text-dark-base-content;
}

/* Dropdown styles */
::ng-deep .ql-toolbar .ql-picker-options .ql-picker-item {
  @apply text-base-content dark:text-dark-base-content;
}

::ng-deep .ql-toolbar .ql-picker-options .ql-picker-item:hover {
  @apply bg-base-200 dark:bg-dark-base-200;
  @apply text-primary dark:text-dark-primary;
}

/* Ensure toolbar separators are visible */
::ng-deep .ql-toolbar.ql-snow .ql-formats {
  margin-right: 15px;
}

/* Fix alignment dropdown */
::ng-deep .ql-toolbar .ql-align .ql-picker-options {
  width: auto;
}

/* Scrollbar styling for the editor */
::ng-deep .ql-editor::-webkit-scrollbar {
  width: 8px;
}

::ng-deep .ql-editor::-webkit-scrollbar-track {
  @apply bg-base-200 dark:bg-dark-base-200;
  border-radius: 4px;
}

::ng-deep .ql-editor::-webkit-scrollbar-thumb {
  @apply bg-base-400 dark:bg-dark-base-400;
  border-radius: 4px;
}

::ng-deep .ql-editor::-webkit-scrollbar-thumb:hover {
  @apply bg-primary dark:bg-dark-primary;
}