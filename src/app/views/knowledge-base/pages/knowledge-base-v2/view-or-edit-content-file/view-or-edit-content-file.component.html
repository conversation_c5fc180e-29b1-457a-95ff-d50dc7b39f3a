<div class="h-full relative flex flex-col rounded-3xl">
  <div
    class="absolute top-0 left-0 right-0 z-2 w-full flex items-center justify-between px-6 py-5 border-b border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100"
  >
    <div
      class="flex items-center text-2xl font-bold text-base-content dark:text-dark-base-content"
    >
      {{ data.file.name || "File Content" }}
    </div>
    <div class="flex items-center justify-end space-x-4">
      <app-svg-icon
        type="icClose"
        class="w-6 h-6 !text-neutral-content dark:!text-dark-neutral-content cursor-pointer"
        (click)="dialogRef.close()"
      ></app-svg-icon>
    </div>
  </div>
  <div
    class="flex-1 overflow-hidden mt-18 mb-20 px-6 pt-6 pb-6 flex flex-col"
  >
    <div class="relative flex-1 flex flex-col">
      <button
        class="absolute top-0 right-0 z-10 flex items-center justify-center w-10 h-10 rounded-lg bg-base-300 dark:bg-dark-base-300 border border-primary-border dark:border-dark-primary-border cursor-pointer hover:bg-base-400 dark:hover:bg-dark-base-400 transition-colors"
        (click)="copyContent()"
        title="Copy content"
      >
        <app-svg-icon
          type="icCopy"
          class="w-5 h-5 !text-primary dark:!text-dark-primary"
        ></app-svg-icon>
      </button>
      <!-- View Mode: Simple scrollable content -->
      @if (!isEditingContent()) {
      <div class="content-viewer flex-1 flex flex-col mt-12">
        <div class="content-container flex-1 overflow-hidden rounded-lg border border-primary-border dark:border-dark-primary-border bg-base-100 dark:bg-dark-base-100">
          <div class="content-display flex-1 overflow-y-auto p-4" [innerHTML]="getFormattedContent()">
          </div>
        </div>
      </div>
      }

      <!-- Edit Mode: Quill Editor -->
      @if (isEditingContent()) {
      <div class="quill-wrapper flex-1 flex flex-col mt-12">
        <quill-editor
          [(ngModel)]="fileContentValue"
          [modules]="quillModules"
          [readOnly]="false"
          placeholder="No content available"
          class="flex-1 flex flex-col"
        ></quill-editor>
      </div>
      }
    </div>
  </div>

  <div
    class="absolute left-0 bottom-0 right-0 z-2 flex items-center justify-end space-x-4 px-6 py-5 border-t border-primary-border dark:border-dark-primary-border bg-base-200 dark:bg-dark-base-200"
  >
    <button dxButton="elevated" (click)="dialogRef.close()">Close</button>
    @if (isEditingContent()) {
    <button
      dxLoadingButton="filled"
      [loading]="isSaving()"
      (click)="saveFileContent()"
    >
      Save and Retrain
    </button>
    } @else {
    <button dxButton="filled" (click)="toggleEditContent()">
      <div class="flex items-center">
<!--        <app-svg-icon type="icEdit" class="w-6 h-6 !text-white"></app-svg-icon>-->
        <span>Edit</span>
      </div>
    </button>
    }
  </div>
</div>
