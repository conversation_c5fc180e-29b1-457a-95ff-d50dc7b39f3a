import { Component, inject, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  DIALOG_DATA,
  DxButton,
  DxDialog,
  DxDialogRef,
  DxFormField,
  DxInput,
  DxLoadingButton,
  DxPrefix,
  DxSnackBar,
} from '@dx-ui/ui';
import { STUDIO_STATUS } from '@core/constants';
import { ConfirmDialogComponent, SvgIconComponent } from '@shared/components';
import { AutosizeDirective } from '@shared/directives';
import { IFile } from '@shared/models';
import { KnowledgeBaseService } from '@shared/services';
import { ClipboardService } from 'ngx-clipboard';
import { QuillEditorComponent, QuillModule } from 'ngx-quill';

@Component({
  selector: 'app-view-or-edit-content-file',
  imports: [
    CommonModule,
    FormsModule,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
    DxFormField,
    DxInput,
    AutosizeDirective,
    DxPrefix,
    QuillModule,
  ],
  templateUrl: './view-or-edit-content-file.component.html',
  styleUrl: './view-or-edit-content-file.component.css',
  host: {
    class: 'h-full',
  },
})
export class ViewOrEditContentFileComponent implements OnInit {
  isEditingContent = signal(false);
  isSaving = signal(false);
  fileContentValue: string = '';
  dialogRef = inject(DxDialogRef<ViewOrEditContentFileComponent>);
  data: { file: IFile; content: string } = inject(DIALOG_DATA);
  private clipboardService: ClipboardService = inject(ClipboardService);
  snackBar = inject(DxSnackBar);
  private dialogService = inject(DxDialog);
  private knowledgeBaseService: KnowledgeBaseService =
    inject(KnowledgeBaseService);

  quillModules = {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline'],
        [{ 'header': [1, 2, 3, false] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        ['blockquote', 'code-block'],
        ['link'],
        ['clean']
      ]
    }
  };

  ngOnInit() {
    this.fileContentValue = this.data.content || '';
  }

  saveFileContent() {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Save and retrain',
          content: 'Are you sure save and retrain this file?',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          this.isSaving.set(true);
          const body = {
            file_id: this.data.file.id,
            content: this.fileContentValue,
          };
          this.knowledgeBaseService.retrainDocument(body).subscribe({
            next: (res) => {
              this.snackBar.open('Retraining file', '', {
                panelClass: 'dx-snack-bar-success',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
              this.isEditingContent.set(false);
              this.isSaving.set(false);
              this.dialogRef.close();
            },
            error: () => {
              this.snackBar.open('Retraining failed', '', {
                panelClass: 'dx-snack-bar-error',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
              this.isSaving.set(false);
              this.dialogRef.close();
            },
          });
        }
      });
  }

  toggleEditContent() {
    this.isEditingContent.set(!this.isEditingContent());
  }

  canSaving() {
    return this.fileContentValue !== this.data.content;
  }

  protected readonly STUDIO_STATUS = STUDIO_STATUS;

  copyContent() {
    this.clipboardService.copy(this.fileContentValue || '');
    this.showSnackBar('Copied!', 'success');
  }
  showSnackBar(message: string, type: 'success' | 'error') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
