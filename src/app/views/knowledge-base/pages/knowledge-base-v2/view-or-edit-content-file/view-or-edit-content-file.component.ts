import { Component, inject, signal, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Dom<PERSON>anitizer, SafeHtml } from '@angular/platform-browser';
import {
  DIALOG_DATA,
  DxButton,
  DxDialog,
  DxDialogRef,
  DxLoadingButton,
  DxSnackBar,
} from '@dx-ui/ui';
import { STUDIO_STATUS } from '@core/constants';
import { ConfirmDialogComponent, SvgIconComponent } from '@shared/components';
import { IFile } from '@shared/models';
import { KnowledgeBaseService } from '@shared/services';
import { ClipboardService } from 'ngx-clipboard';
import { QuillEditorComponent, QuillModule } from 'ngx-quill';

@Component({
  selector: 'app-view-or-edit-content-file',
  imports: [
    CommonModule,
    FormsModule,
    SvgIconComponent,
    DxButton,
    DxLoadingButton,
    QuillModule,
  ],
  templateUrl: './view-or-edit-content-file.component.html',
  styleUrl: './view-or-edit-content-file.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'h-full',
  },
})
export class ViewOrEditContentFileComponent implements OnInit {
  isEditingContent = signal(false);
  isSaving = signal(false);
  fileContentValue: string = '';
  dialogRef = inject(DxDialogRef<ViewOrEditContentFileComponent>);
  data: { file: IFile; content: string } = inject(DIALOG_DATA);
  private clipboardService: ClipboardService = inject(ClipboardService);
  snackBar = inject(DxSnackBar);
  private dialogService = inject(DxDialog);
  private knowledgeBaseService: KnowledgeBaseService =
    inject(KnowledgeBaseService);
  private sanitizer = inject(DomSanitizer);
  private cdr = inject(ChangeDetectorRef);

  quillModules = {
    toolbar: {
      container: [
        ['bold', 'italic', 'underline'],
        [{ 'header': [1, 2, 3, false] }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        ['blockquote', 'code-block'],
        ['link'],
        ['clean']
      ]
    }
  };

  ngOnInit() {
    // Initialize with proper line break handling
    let initialContent = this.data.content || '';

    // Normalize line endings for consistent handling
    if (initialContent) {
      initialContent = initialContent
        .replace(/\r\n/g, '\n')  // Normalize Windows line endings
        .replace(/\r/g, '\n');   // Normalize Mac line endings
    }

    this.fileContentValue = initialContent;
  }

  saveFileContent() {
    this.dialogService
      .open(ConfirmDialogComponent, {
        data: {
          title: 'Save and retrain',
          content: 'Are you sure save and retrain this file?',
        },
        width: '300px',
      })
      .afterClosed()
      .subscribe((value: any) => {
        if (!!value) {
          this.isSaving.set(true);
          const body = {
            file_id: this.data.file.id,
            content: this.fileContentValue,
          };
          this.knowledgeBaseService.retrainDocument(body).subscribe({
            next: (res) => {
              this.snackBar.open('Retraining file', '', {
                panelClass: 'dx-snack-bar-success',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
              this.isEditingContent.set(false);
              this.isSaving.set(false);
              this.dialogRef.close();
            },
            error: () => {
              this.snackBar.open('Retraining failed', '', {
                panelClass: 'dx-snack-bar-error',
                duration: 5000,
                verticalPosition: 'top',
                horizontalPosition: 'right',
              });
              this.isSaving.set(false);
              this.dialogRef.close();
            },
          });
        }
      });
  }

  toggleEditContent() {
    if (!this.isEditingContent()) {
      // Switching to edit mode - ensure fileContentValue is up to date and preserve line breaks
      let contentForEdit = this.data.content || '';

      // If switching from view to edit, ensure line breaks are properly formatted for Quill
      if (contentForEdit && !this.hasHtmlContent()) {
        // For plain text, ensure line breaks are preserved
        contentForEdit = contentForEdit
          .replace(/\r\n/g, '\n')  // Normalize Windows line endings
          .replace(/\r/g, '\n');   // Normalize Mac line endings
      }

      this.fileContentValue = contentForEdit;
    }

    this.isEditingContent.set(!this.isEditingContent());

    // Clear cache when switching modes
    this._formattedContent = null;
    this._hasHtmlContent = null;
    this._lastContentValue = '';
    this.cdr.markForCheck();
  }

  canSaving() {
    return this.fileContentValue !== this.data.content;
  }

  protected readonly STUDIO_STATUS = STUDIO_STATUS;

  private _formattedContent: SafeHtml | null = null;
  private _lastContentValue: string = '';
  private _hasHtmlContent: boolean | null = null;

  hasHtmlContent(): boolean {
    if (this._lastContentValue === this.fileContentValue && this._hasHtmlContent !== null) {
      return this._hasHtmlContent;
    }

    this._hasHtmlContent = /<[^>]*>/g.test(this.fileContentValue || '');
    this._lastContentValue = this.fileContentValue;
    return this._hasHtmlContent;
  }

  getPlainTextContent(): string {
    if (!this.fileContentValue) {
      return 'No content available';
    }

    // Ensure proper line breaks are preserved for plain text display
    return this.fileContentValue
      .replace(/\r\n/g, '\n') // Normalize Windows line endings
      .replace(/\r/g, '\n');  // Normalize Mac line endings
  }

  getFormattedContent(): SafeHtml {
    // Only use this for HTML content
    if (!this.hasHtmlContent()) {
      return this.sanitizer.bypassSecurityTrustHtml('');
    }

    // Cache the formatted content to avoid re-processing on every change detection
    if (this._lastContentValue === this.fileContentValue && this._formattedContent) {
      return this._formattedContent;
    }

    if (!this.fileContentValue) {
      this._formattedContent = this.sanitizer.bypassSecurityTrustHtml('<p class="text-neutral-content dark:text-dark-neutral-content opacity-60">No content available</p>');
      return this._formattedContent;
    }

    // For HTML content, ensure proper line break handling
    let htmlContent = this.fileContentValue;

    // If it's HTML but doesn't have proper paragraph tags, convert line breaks
    if (!htmlContent.includes('<p>') && !htmlContent.includes('<div>') && !htmlContent.includes('<br>')) {
      htmlContent = htmlContent
        .replace(/\r\n/g, '\n') // Normalize Windows line endings
        .replace(/\r/g, '\n')   // Normalize Mac line endings
        .replace(/\n\n+/g, '</p><p>') // Double line breaks become paragraph breaks
        .replace(/\n/g, '<br>') // Single line breaks become <br> tags
        .replace(/^/, '<p>')    // Add opening paragraph tag
        .replace(/$/, '</p>');  // Add closing paragraph tag
    }

    this._formattedContent = this.sanitizer.bypassSecurityTrustHtml(htmlContent);
    return this._formattedContent;
  }

  copyContent() {
    let contentToCopy = this.isEditingContent()
      ? this.fileContentValue || ''
      : this.data.content || '';

    // If it's HTML content, strip HTML tags but preserve line breaks
    if (this.hasHtmlContent() && contentToCopy) {
      contentToCopy = contentToCopy
        .replace(/<br\s*\/?>/gi, '\n')     // Convert <br> tags to line breaks
        .replace(/<\/p>\s*<p>/gi, '\n\n')  // Convert paragraph breaks to double line breaks
        .replace(/<[^>]*>/g, '')           // Strip all other HTML tags
        .replace(/&nbsp;/g, ' ')           // Convert non-breaking spaces
        .replace(/&amp;/g, '&')            // Convert HTML entities
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .trim();
    }

    this.clipboardService.copy(contentToCopy);
    this.showSnackBar('Copied!', 'success');
  }
  showSnackBar(message: string, type: 'success' | 'error') {
    let panelClass: string = 'dx-snack-bar-info';
    if (type === 'success') {
      panelClass = 'dx-snack-bar-success';
    } else if (type === 'error') {
      panelClass = 'dx-snack-bar-error';
    }
    this.snackBar.open(message, '', {
      panelClass: panelClass,
      duration: 5000,
      verticalPosition: 'top',
      horizontalPosition: 'right',
    });
  }
}
